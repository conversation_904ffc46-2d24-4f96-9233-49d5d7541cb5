# IoT 网关协议客户端 Demo

这是一个支持多种工业协议的模拟客户端演示程序，可以持续发送数据并与各种工业设备进行通信。

## 支持的协议

- **OPC UA** - 现代工业通信标准
- **OPC DA** - 传统OPC数据访问协议（模拟实现）
- **Modbus TCP** - 基于TCP/IP的Modbus协议
- **Modbus RTU** - 基于串口的Modbus协议

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/nimblex/iot/
│   │       ├── client/                 # 协议客户端接口和实现
│   │       │   ├── ProtocolClient.java # 通用协议客户端接口
│   │       │   └── impl/               # 各协议的具体实现
│   │       │       ├── OpcUaClient.java
│   │       │       ├── OpcDaClient.java
│   │       │       ├── ModbusTcpClient.java
│   │       │       └── ModbusRtuClient.java
│   │       ├── config/                 # 配置管理
│   │       │   └── ProtocolConfig.java
│   │       ├── factory/                # 工厂类
│   │       │   └── ProtocolClientFactory.java
│   │       ├── service/                # 服务层
│   │       │   └── ProtocolClientService.java
│   │       └── server/                 # 主程序
│   │           └── Main.java
│   └── resources/
│       ├── application.properties      # 配置文件
│       └── logback.xml                # 日志配置
└── test/
    └── java/                          # 测试代码
```

## 配置说明

在 `src/main/resources/application.properties` 文件中配置协议参数：

### 通用配置
```properties
# 协议类型: OPC_DA, OPC_UA, MODBUS_TCP, MODBUS_RTU
protocol.type=OPC_UA

# 数据读取间隔（毫秒）
client.read.interval=5000

# 是否自动启动
client.auto.start=true

# 最大重试次数
client.max.retries=3

# 重试延迟（毫秒）
client.retry.delay=1000
```

### OPC UA 配置
```properties
opcua.endpoint.url=opc.tcp://localhost:4840
opcua.security.policy=None
opcua.message.security.mode=None
opcua.username=
opcua.password=
opcua.node.ids=ns=2;i=2,ns=2;i=3,ns=2;i=4
```

### OPC DA 配置
```properties
opcda.server.prog.id=Matrikon.OPC.Simulation.1
opcda.server.host=localhost
opcda.server.domain=
opcda.server.username=
opcda.server.password=
opcda.item.ids=Random.Int1,Random.Int2,Random.Real4
```

### Modbus TCP 配置
```properties
modbus.tcp.host=localhost
modbus.tcp.port=502
modbus.tcp.unit.id=1
modbus.tcp.addresses=0,1,2,3,4
```

### Modbus RTU 配置
```properties
modbus.rtu.port=COM1
modbus.rtu.baud.rate=9600
modbus.rtu.data.bits=8
modbus.rtu.stop.bits=1
modbus.rtu.parity=NONE
modbus.rtu.unit.id=1
modbus.rtu.addresses=0,1,2,3,4
```

## 运行方式

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 运行程序
```bash
mvn exec:java -Dexec.mainClass="com.nimblex.iot.server.Main"
```

或者直接运行：
```bash
java -cp target/classes:target/dependency/* com.nimblex.iot.server.Main
```

### 3. 交互命令

程序启动后，可以使用以下命令：

- `status` - 查看服务状态
- `read` - 手动读取一次数据
- `write <地址> <值>` - 写入数据到指定地址
- `stop` - 停止服务
- `start` - 启动服务
- `help` - 显示帮助信息
- `quit` - 退出程序

## 功能特性

### 1. 多协议支持
- 统一的协议客户端接口
- 工厂模式创建不同协议的客户端
- 配置文件驱动的协议选择

### 2. 自动重连
- 连接失败时自动重试
- 连接丢失时自动重连
- 可配置的重试次数和延迟

### 3. 定时数据读取
- 可配置的读取间隔
- 异步数据处理
- JSON格式的数据输出

### 4. 交互式操作
- 命令行界面
- 实时状态查看
- 手动数据读写

### 5. 完善的日志
- 分级日志输出
- 文件日志滚动
- 详细的操作记录

## 依赖说明

### OPC UA
- 使用 Eclipse Milo SDK
- 支持各种安全策略
- 支持订阅和读写操作

### Modbus
- 使用 digitalpetri modbus 库
- 支持 TCP 和 RTU 两种模式
- 支持串口通信

### OPC DA
- 使用 j-interop 库（当前为模拟实现）
- 需要 Windows 环境和 COM 组件
- 支持传统的 OPC DA 服务器

## 注意事项

1. **OPC DA**: 当前提供的是模拟实现，真实的OPC DA需要Windows环境和相应的COM组件支持。

2. **Modbus RTU**: 需要系统有可用的串口，并且串口参数配置正确。

3. **网络协议**: 确保目标服务器可访问，防火墙设置正确。

4. **权限**: 某些操作可能需要管理员权限，特别是串口访问。

## 扩展开发

### 添加新协议
1. 实现 `ProtocolClient` 接口
2. 在 `ProtocolClientFactory` 中添加创建逻辑
3. 在配置文件中添加相应的配置项
4. 更新枚举类型

### 自定义数据处理
重写 `ProtocolClientService.processData()` 方法来实现自定义的数据处理逻辑，比如：
- 数据存储到数据库
- 发送到消息队列
- 数据格式转换
- 异常数据告警

## 故障排除

### 常见问题
1. **连接失败**: 检查网络连接和服务器配置
2. **串口问题**: 确认串口名称和参数设置
3. **权限问题**: 以管理员身份运行程序
4. **依赖问题**: 确保所有Maven依赖正确下载

### 日志查看
程序会在 `logs/` 目录下生成日志文件，可以查看详细的运行信息和错误信息。
