# ????
# ???????: OPC_DA, OPC_UA, MODBUS_TCP, MODBUS_RTU
protocol.type=OPC_UA

# OPC UA ??
opcua.endpoint.url=opc.tcp://localhost:4840
opcua.security.policy=None
opcua.message.security.mode=None
opcua.username=
opcua.password=
opcua.node.ids=ns=2;i=2,ns=2;i=3,ns=2;i=4

# OPC DA ??
opcda.server.prog.id=Matrikon.OPC.Simulation.1
opcda.server.host=localhost
opcda.server.domain=
opcda.server.username=
opcda.server.password=
opcda.item.ids=Random.Int1,Random.Int2,Random.Real4

# Modbus TCP ??
modbus.tcp.host=localhost
modbus.tcp.port=502
modbus.tcp.unit.id=1
modbus.tcp.addresses=0,1,2,3,4

# Modbus RTU ??
modbus.rtu.port=COM1
modbus.rtu.baud.rate=9600
modbus.rtu.data.bits=8
modbus.rtu.stop.bits=1
modbus.rtu.parity=NONE
modbus.rtu.unit.id=1
modbus.rtu.addresses=0,1,2,3,4

# ????
client.read.interval=5000
client.auto.start=true
client.max.retries=3
client.retry.delay=1000

# ????
logging.level=INFO
