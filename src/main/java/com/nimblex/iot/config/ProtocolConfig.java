package com.nimblex.iot.config;

import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 协议配置管理类
 */
public class ProtocolConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(ProtocolConfig.class);
    
    private Configuration config;
    
    public ProtocolConfig() {
        loadConfiguration();
    }
    
    private void loadConfiguration() {
        try {
            config = new PropertiesConfiguration("application.properties");
            logger.info("成功加载配置文件");
        } catch (ConfigurationException e) {
            logger.error("加载配置文件失败", e);
            throw new RuntimeException("无法加载配置文件", e);
        }
    }
    
    // 通用配置
    public String getProtocolType() {
        return config.getString("protocol.type", "OPC_UA");
    }
    
    public int getReadInterval() {
        return config.getInt("client.read.interval", 5000);
    }
    
    public boolean isAutoStart() {
        return config.getBoolean("client.auto.start", true);
    }
    
    public int getMaxRetries() {
        return config.getInt("client.max.retries", 3);
    }
    
    public int getRetryDelay() {
        return config.getInt("client.retry.delay", 1000);
    }
    
    // OPC UA 配置
    public String getOpcUaEndpointUrl() {
        return config.getString("opcua.endpoint.url", "opc.tcp://localhost:4840");
    }
    
    public String getOpcUaSecurityPolicy() {
        return config.getString("opcua.security.policy", "None");
    }
    
    public String getOpcUaMessageSecurityMode() {
        return config.getString("opcua.message.security.mode", "None");
    }
    
    public String getOpcUaUsername() {
        return config.getString("opcua.username", "");
    }
    
    public String getOpcUaPassword() {
        return config.getString("opcua.password", "");
    }
    
    public List<String> getOpcUaNodeIds() {
        String nodeIds = config.getString("opcua.node.ids", "ns=2;i=2,ns=2;i=3,ns=2;i=4");
        return Arrays.asList(nodeIds.split(","));
    }
    
    // OPC DA 配置
    public String getOpcDaServerProgId() {
        return config.getString("opcda.server.prog.id", "Matrikon.OPC.Simulation.1");
    }
    
    public String getOpcDaServerHost() {
        return config.getString("opcda.server.host", "localhost");
    }
    
    public String getOpcDaDomain() {
        return config.getString("opcda.server.domain", "");
    }
    
    public String getOpcDaUsername() {
        return config.getString("opcda.server.username", "");
    }
    
    public String getOpcDaPassword() {
        return config.getString("opcda.server.password", "");
    }
    
    public List<String> getOpcDaItemIds() {
        String itemIds = config.getString("opcda.item.ids", "Random.Int1,Random.Int2,Random.Real4");
        return Arrays.asList(itemIds.split(","));
    }
    
    // Modbus TCP 配置
    public String getModbusTcpHost() {
        return config.getString("modbus.tcp.host", "localhost");
    }
    
    public int getModbusTcpPort() {
        return config.getInt("modbus.tcp.port", 502);
    }
    
    public int getModbusTcpUnitId() {
        return config.getInt("modbus.tcp.unit.id", 1);
    }
    
    public List<Integer> getModbusTcpAddresses() {
        String addresses = config.getString("modbus.tcp.addresses", "0,1,2,3,4");
        return Arrays.stream(addresses.split(","))
            .map(String::trim)
            .map(Integer::parseInt)
            .collect(Collectors.toList());
    }
    
    // Modbus RTU 配置
    public String getModbusRtuPort() {
        return config.getString("modbus.rtu.port", "COM1");
    }
    
    public int getModbusRtuBaudRate() {
        return config.getInt("modbus.rtu.baud.rate", 9600);
    }
    
    public int getModbusRtuDataBits() {
        return config.getInt("modbus.rtu.data.bits", 8);
    }
    
    public int getModbusRtuStopBits() {
        return config.getInt("modbus.rtu.stop.bits", 1);
    }
    
    public String getModbusRtuParity() {
        return config.getString("modbus.rtu.parity", "NONE");
    }
    
    public int getModbusRtuUnitId() {
        return config.getInt("modbus.rtu.unit.id", 1);
    }
    
    public List<Integer> getModbusRtuAddresses() {
        String addresses = config.getString("modbus.rtu.addresses", "0,1,2,3,4");
        return Arrays.stream(addresses.split(","))
            .map(String::trim)
            .map(Integer::parseInt)
            .collect(Collectors.toList());
    }
}
