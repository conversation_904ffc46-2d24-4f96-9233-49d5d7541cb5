package com.nimblex.iot.factory;

import com.nimblex.iot.client.ProtocolClient;
import com.nimblex.iot.client.impl.ModbusRtuClient;
import com.nimblex.iot.client.impl.ModbusTcpClient;
import com.nimblex.iot.client.impl.OpcDaClient;
import com.nimblex.iot.client.impl.OpcUaClient;
import com.nimblex.iot.config.ProtocolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 协议客户端工厂类
 */
public class ProtocolClientFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(ProtocolClientFactory.class);
    
    /**
     * 支持的协议类型枚举
     */
    public enum ProtocolType {
        OPC_DA,
        OPC_UA,
        MODBUS_TCP,
        MODBUS_RTU
    }
    
    /**
     * 根据配置创建协议客户端
     * @param config 协议配置
     * @return 协议客户端实例
     */
    public static ProtocolClient createClient(ProtocolConfig config) {
        String protocolTypeStr = config.getProtocolType();
        ProtocolType protocolType;
        
        try {
            protocolType = ProtocolType.valueOf(protocolTypeStr);
        } catch (IllegalArgumentException e) {
            logger.error("不支持的协议类型: {}", protocolTypeStr);
            throw new IllegalArgumentException("不支持的协议类型: " + protocolTypeStr);
        }
        
        logger.info("正在创建 {} 客户端", protocolType);
        
        switch (protocolType) {
            case OPC_UA:
                return createOpcUaClient(config);
            case OPC_DA:
                return createOpcDaClient(config);
            case MODBUS_TCP:
                return createModbusTcpClient(config);
            case MODBUS_RTU:
                return createModbusRtuClient(config);
            default:
                throw new IllegalArgumentException("不支持的协议类型: " + protocolType);
        }
    }
    
    /**
     * 创建 OPC UA 客户端
     */
    private static ProtocolClient createOpcUaClient(ProtocolConfig config) {
        return new OpcUaClient(
            config.getOpcUaEndpointUrl(),
            config.getOpcUaSecurityPolicy(),
            config.getOpcUaMessageSecurityMode(),
            config.getOpcUaUsername(),
            config.getOpcUaPassword(),
            config.getOpcUaNodeIds()
        );
    }
    
    /**
     * 创建 OPC DA 客户端
     */
    private static ProtocolClient createOpcDaClient(ProtocolConfig config) {
        return new OpcDaClient(
            config.getOpcDaServerProgId(),
            config.getOpcDaServerHost(),
            config.getOpcDaDomain(),
            config.getOpcDaUsername(),
            config.getOpcDaPassword(),
            config.getOpcDaItemIds()
        );
    }
    
    /**
     * 创建 Modbus TCP 客户端
     */
    private static ProtocolClient createModbusTcpClient(ProtocolConfig config) {
        return new ModbusTcpClient(
            config.getModbusTcpHost(),
            config.getModbusTcpPort(),
            config.getModbusTcpUnitId(),
            config.getModbusTcpAddresses()
        );
    }
    
    /**
     * 创建 Modbus RTU 客户端
     */
    private static ProtocolClient createModbusRtuClient(ProtocolConfig config) {
        return new ModbusRtuClient(
            config.getModbusRtuPort(),
            config.getModbusRtuBaudRate(),
            config.getModbusRtuDataBits(),
            config.getModbusRtuStopBits(),
            config.getModbusRtuParity(),
            config.getModbusRtuUnitId(),
            config.getModbusRtuAddresses()
        );
    }
}
