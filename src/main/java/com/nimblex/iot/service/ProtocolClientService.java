package com.nimblex.iot.service;

//import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimblex.iot.client.ProtocolClient;
import com.nimblex.iot.config.ProtocolConfig;
import com.nimblex.iot.factory.ProtocolClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 协议客户端服务类
 * 负责管理协议客户端的生命周期和数据读取
 */
public class ProtocolClientService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProtocolClientService.class);
    
    private final ProtocolConfig config;
    private final ObjectMapper objectMapper;
    private ProtocolClient client;
    private ScheduledExecutorService scheduler;
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    public ProtocolClientService() {
        this.config = new ProtocolConfig();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 启动服务
     */
    public void start() {
        if (running.get()) {
            logger.warn("服务已经在运行中");
            return;
        }
        
        try {
            logger.info("正在启动协议客户端服务...");
            
            // 创建客户端
            client = ProtocolClientFactory.createClient(config);
            logger.info("创建客户端成功: {}", client.getConnectionInfo());
            
            // 连接客户端
            if (!connectWithRetry()) {
                logger.error("连接失败，服务启动失败");
                return;
            }
            
            // 启动定时读取任务
            startScheduledReading();
            
            running.set(true);
            logger.info("协议客户端服务启动成功");
            
        } catch (Exception e) {
            logger.error("启动协议客户端服务失败", e);
            stop();
        }
    }
    
    /**
     * 停止服务
     */
    public void stop() {
        if (!running.get()) {
            logger.warn("服务未在运行");
            return;
        }
        
        logger.info("正在停止协议客户端服务...");
        
        running.set(false);
        
        // 停止定时任务
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 断开客户端连接
        if (client != null) {
            client.disconnect();
        }
        
        logger.info("协议客户端服务已停止");
    }
    
    /**
     * 检查服务是否在运行
     */
    public boolean isRunning() {
        return running.get();
    }
    
    /**
     * 获取客户端信息
     */
    public String getClientInfo() {
        if (client != null) {
            return client.getConnectionInfo();
        }
        return "客户端未初始化";
    }
    
    /**
     * 手动读取一次数据
     */
    public Map<String, Object> readDataOnce() {
        if (client != null && client.isConnected()) {
            return client.readData();
        }
        logger.warn("客户端未连接，无法读取数据");
        return null;
    }
    
    /**
     * 写入数据
     */
    public boolean writeData(String address, Object value) {
        if (client != null && client.isConnected()) {
            return client.writeData(address, value);
        }
        logger.warn("客户端未连接，无法写入数据");
        return false;
    }
    
    /**
     * 带重试的连接方法
     */
    private boolean connectWithRetry() {
        int maxRetries = config.getMaxRetries();
        int retryDelay = config.getRetryDelay();
        
        for (int i = 0; i < maxRetries; i++) {
            logger.info("尝试连接... (第 {}/{} 次)", i + 1, maxRetries);
            
            if (client.connect()) {
                logger.info("连接成功");
                return true;
            }
            
            if (i < maxRetries - 1) {
                logger.warn("连接失败，{}ms 后重试", retryDelay);
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("连接重试被中断");
                    return false;
                }
            }
        }
        
        logger.error("连接失败，已达到最大重试次数");
        return false;
    }
    
    /**
     * 启动定时读取任务
     */
    private void startScheduledReading() {
        int readInterval = config.getReadInterval();
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ProtocolClient-Reader");
            t.setDaemon(true);
            return t;
        });
        
        scheduler.scheduleAtFixedRate(this::performScheduledRead, 0, readInterval, TimeUnit.MILLISECONDS);
        logger.info("定时读取任务已启动，间隔: {}ms", readInterval);
    }
    
    /**
     * 执行定时读取
     */
    private void performScheduledRead() {
        if (!running.get() || client == null) {
            return;
        }
        
        try {
            if (!client.isConnected()) {
                logger.warn("客户端连接丢失，尝试重新连接...");
                if (!connectWithRetry()) {
                    logger.error("重新连接失败");
                    return;
                }
            }
            
            Map<String, Object> data = client.readData();
            if (data != null && !data.isEmpty()) {
                // 输出读取到的数据
                String jsonData = objectMapper.writeValueAsString(data);
                logger.info("读取数据: {}", jsonData);
                
                // 这里可以添加数据处理逻辑，比如：
                // - 发送到消息队列
                // - 存储到数据库
                // - 转发到其他系统
                processData(data);
            } else {
                logger.debug("未读取到数据");
            }
            
        } catch (Exception e) {
            logger.error("定时读取数据时出错", e);
        }
    }
    
    /**
     * 处理读取到的数据
     * 子类可以重写此方法来实现自定义的数据处理逻辑
     */
    protected void processData(Map<String, Object> data) {
        // 默认实现：仅记录数据
        logger.debug("处理数据: {} 个数据点", data.size());
        
        // 示例：检查数据异常
        data.forEach((key, value) -> {
            if (value instanceof Number) {
                Number numValue = (Number) value;
                if (numValue.doubleValue() > 1000) {
                    logger.warn("数据点 {} 的值 {} 超过阈值", key, value);
                }
            }
        });
    }
}
