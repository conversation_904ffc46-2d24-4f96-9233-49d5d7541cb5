package com.nimblex.iot.server;

import com.nimblex.iot.service.ProtocolClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Scanner;

/**
 * IoT 网关协议客户端主程序
 * 支持 OPC DA、OPC UA、Modbus TCP、Modbus RTU 等协议
 */
public class Main {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);
    private static ProtocolClientService service;

    public static void main(String[] args) {
        logger.info("=== IoT 网关协议客户端 Demo ===");
        logger.info("支持协议: OPC DA, OPC UA, Modbus TCP, Modbus RTU");
        logger.info("配置文件: src/main/resources/application.properties");

        // 创建服务实例
        service = new ProtocolClientService();

        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("正在关闭应用程序...");
            if (service != null) {
                service.stop();
            }
        }));

        // 启动服务
        service.start();

        if (service.isRunning()) {
            logger.info("服务启动成功！客户端信息: {}", service.getClientInfo());
            logger.info("输入命令进行操作:");
            logger.info("  status  - 查看状态");
            logger.info("  read    - 手动读取一次数据");
            logger.info("  write   - 写入数据 (格式: write <地址> <值>)");
            logger.info("  stop    - 停止服务");
            logger.info("  start   - 启动服务");
            logger.info("  quit    - 退出程序");

            // 启动命令行交互
            startCommandLineInterface();
        } else {
            logger.error("服务启动失败，程序退出");
            System.exit(1);
        }
    }

    /**
     * 启动命令行交互界面
     */
    private static void startCommandLineInterface() {
        Scanner scanner = new Scanner(System.in);

        while (true) {
            System.out.print("> ");
            String input = scanner.nextLine().trim();

            if (input.isEmpty()) {
                continue;
            }

            String[] parts = input.split("\\s+");
            String command = parts[0].toLowerCase();

            try {
                switch (command) {
                    case "status":
                        handleStatusCommand();
                        break;
                    case "read":
                        handleReadCommand();
                        break;
                    case "write":
                        handleWriteCommand(parts);
                        break;
                    case "stop":
                        handleStopCommand();
                        break;
                    case "start":
                        handleStartCommand();
                        break;
                    case "quit":
                    case "exit":
                        handleQuitCommand();
                        return;
                    case "help":
                        printHelp();
                        break;
                    default:
                        System.out.println("未知命令: " + command + "。输入 'help' 查看帮助。");
                }
            } catch (Exception e) {
                logger.error("执行命令时出错: " + command, e);
                System.out.println("命令执行失败: " + e.getMessage());
            }
        }
    }

    private static void handleStatusCommand() {
        System.out.println("=== 服务状态 ===");
        System.out.println("运行状态: " + (service.isRunning() ? "运行中" : "已停止"));
        System.out.println("客户端信息: " + service.getClientInfo());
    }

    private static void handleReadCommand() {
        System.out.println("正在读取数据...");
        var data = service.readDataOnce();
        if (data != null && !data.isEmpty()) {
            System.out.println("读取到的数据:");
            data.forEach((key, value) ->
                System.out.println("  " + key + " = " + value));
        } else {
            System.out.println("未读取到数据");
        }
    }

    private static void handleWriteCommand(String[] parts) {
        if (parts.length < 3) {
            System.out.println("写入命令格式: write <地址> <值>");
            return;
        }

        String address = parts[1];
        String value = parts[2];

        System.out.println("正在写入数据: " + address + " = " + value);
        boolean success = service.writeData(address, value);
        System.out.println(success ? "写入成功" : "写入失败");
    }

    private static void handleStopCommand() {
        System.out.println("正在停止服务...");
        service.stop();
        System.out.println("服务已停止");
    }

    private static void handleStartCommand() {
        if (service.isRunning()) {
            System.out.println("服务已在运行中");
        } else {
            System.out.println("正在启动服务...");
            service.start();
            System.out.println(service.isRunning() ? "服务启动成功" : "服务启动失败");
        }
    }

    private static void handleQuitCommand() {
        System.out.println("正在退出程序...");
        if (service.isRunning()) {
            service.stop();
        }
        System.out.println("再见！");
    }

    private static void printHelp() {
        System.out.println("=== 可用命令 ===");
        System.out.println("  status  - 查看服务状态");
        System.out.println("  read    - 手动读取一次数据");
        System.out.println("  write   - 写入数据 (格式: write <地址> <值>)");
        System.out.println("  stop    - 停止服务");
        System.out.println("  start   - 启动服务");
        System.out.println("  help    - 显示帮助信息");
        System.out.println("  quit    - 退出程序");
    }
}