package com.nimblex.iot.client;

import java.util.Map;

/**
 * 协议客户端接口
 * 定义所有协议客户端的通用方法
 */
public interface ProtocolClient {
    
    /**
     * 连接到服务器
     * @return 连接是否成功
     */
    boolean connect();
    
    /**
     * 断开连接
     */
    void disconnect();
    
    /**
     * 检查连接状态
     * @return 是否已连接
     */
    boolean isConnected();
    
    /**
     * 读取数据
     * @return 读取到的数据，key为地址/节点ID，value为数据值
     */
    Map<String, Object> readData();
    
    /**
     * 写入数据
     * @param address 地址或节点ID
     * @param value 要写入的值
     * @return 写入是否成功
     */
    boolean writeData(String address, Object value);
    
    /**
     * 获取协议类型
     * @return 协议类型名称
     */
    String getProtocolType();
    
    /**
     * 获取连接信息
     * @return 连接信息描述
     */
    String getConnectionInfo();
}
