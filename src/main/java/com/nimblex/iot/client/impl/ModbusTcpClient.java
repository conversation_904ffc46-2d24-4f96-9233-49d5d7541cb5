package com.nimblex.iot.client.impl;

import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.ghgande.j2mod.modbus.procimg.Register;
import com.nimblex.iot.client.ProtocolClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Modbus TCP 客户端实现
 */
public class ModbusTcpClient implements ProtocolClient {

    private static final Logger logger = LoggerFactory.getLogger(ModbusTcpClient.class);

    private final String host;
    private final int port;
    private final int unitId;
    private final List<Integer> addresses;

    private ModbusTCPMaster master;
    private boolean connected = false;

    public ModbusTcpClient(String host, int port, int unitId, List<Integer> addresses) {
        this.host = host;
        this.port = port;
        this.unitId = unitId;
        this.addresses = addresses;
    }

    @Override
    public boolean connect() {
        try {
            logger.info("正在连接到 Modbus TCP 服务器: {}:{}", host, port);

            master = new ModbusTCPMaster(host, port);
            master.connect();

            connected = true;
            logger.info("成功连接到 Modbus TCP 服务器");
            return true;

        } catch (Exception e) {
            logger.error("连接 Modbus TCP 服务器失败", e);
            connected = false;
            return false;
        }
    }

    @Override
    public void disconnect() {
        if (master != null && connected) {
            try {
                master.disconnect();
                connected = false;
                logger.info("已断开 Modbus TCP 连接");
            } catch (Exception e) {
                logger.error("断开 Modbus TCP 连接时出错", e);
            }
        }
    }

    @Override
    public boolean isConnected() {
        return connected && master != null;
    }

    @Override
    public Map<String, Object> readData() {
        Map<String, Object> data = new HashMap<>();

        if (!isConnected()) {
            logger.warn("Modbus TCP 客户端未连接");
            return data;
        }

        try {
            for (Integer address : addresses) {
                // 读取保持寄存器
                Register[] registers = master.readHoldingRegisters(address, 1);

                if (registers != null && registers.length > 0) {
                    int value = registers[0].getValue();
                    data.put("HR_" + address, value);
                }
            }

            logger.debug("成功读取 {} 个寄存器的数据", data.size());

        } catch (Exception e) {
            logger.error("读取 Modbus TCP 数据时出错", e);
        }

        return data;
    }

    @Override
    public boolean writeData(String address, Object value) {
        if (!isConnected()) {
            logger.warn("Modbus TCP 客户端未连接");
            return false;
        }

        try {
            int addr = Integer.parseInt(address.replace("HR_", ""));
            int val = Integer.parseInt(value.toString());

            master.writeSingleRegister(unitId, addr, val);

            logger.debug("成功写入寄存器 {}: {}", address, value);
            return true;

        } catch (Exception e) {
            logger.error("写入 Modbus TCP 数据时出错", e);
            return false;
        }
    }

    @Override
    public String getProtocolType() {
        return "Modbus TCP";
    }

    @Override
    public String getConnectionInfo() {
        return String.format("Modbus TCP - 主机: %s:%d, 单元ID: %d", host, port, unitId);
    }
}
