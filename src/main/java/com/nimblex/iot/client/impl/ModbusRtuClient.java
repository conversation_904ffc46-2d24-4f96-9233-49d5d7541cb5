package com.nimblex.iot.client.impl;

import com.nimblex.iot.client.ProtocolClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Modbus RTU 客户端实现
 */
public class ModbusRtuClient implements ProtocolClient {

    private static final Logger logger = LoggerFactory.getLogger(ModbusRtuClient.class);

    private final String portName;
    private final int baudRate;
    private final int dataBits;
    private final int stopBits;
    private final String parity;
    private final int unitId;
    private final List<Integer> addresses;

    private ModbusSerialMaster master;
    private boolean connected = false;

    public ModbusRtuClient(String portName, int baudRate, int dataBits, int stopBits,
                          String parity, int unitId, List<Integer> addresses) {
        this.portName = portName;
        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;
        this.unitId = unitId;
        this.addresses = addresses;
    }

    @Override
    public boolean connect() {
        try {
            logger.info("正在连接到 Modbus RTU 设备: {}", portName);

            // 创建串口参数
            SerialParameters params = new SerialParameters();
            params.setPortName(portName);
            params.setBaudRate(baudRate);
            params.setDatabits(dataBits);
            params.setStopbits(stopBits);

            // 设置校验位
            switch (parity.toUpperCase()) {
                case "NONE":
                    params.setParity("None");
                    break;
                case "EVEN":
                    params.setParity("Even");
                    break;
                case "ODD":
                    params.setParity("Odd");
                    break;
                default:
                    params.setParity("None");
            }

            params.setEncoding("rtu");
            params.setEcho(false);

            // 创建 Modbus RTU Master
            master = new ModbusSerialMaster(params);
            master.connect();

            connected = true;
            logger.info("成功连接到 Modbus RTU 设备");
            return true;

        } catch (Exception e) {
            logger.error("连接 Modbus RTU 设备失败", e);
            connected = false;
            return false;
        }
    }

    @Override
    public void disconnect() {
        if (master != null && connected) {
            try {
                master.disconnect();
                connected = false;
                logger.info("已断开 Modbus RTU 连接");
            } catch (Exception e) {
                logger.error("断开 Modbus RTU 连接时出错", e);
            }
        }
    }

    @Override
    public boolean isConnected() {
        return connected && master != null;
    }

    @Override
    public Map<String, Object> readData() {
        Map<String, Object> data = new HashMap<>();

        if (!isConnected()) {
            logger.warn("Modbus RTU 客户端未连接");
            return data;
        }

        try {
            for (Integer address : addresses) {
                // 读取保持寄存器
                Register[] registers = master.readHoldingRegisters(address, 1);

                if (registers != null && registers.length > 0) {
                    int value = registers[0].getValue();
                    data.put("HR_" + address, value);
                }
            }

            logger.debug("成功读取 {} 个寄存器的数据", data.size());

        } catch (Exception e) {
            logger.error("读取 Modbus RTU 数据时出错", e);
        }

        return data;
    }

    @Override
    public boolean writeData(String address, Object value) {
        if (!isConnected()) {
            logger.warn("Modbus RTU 客户端未连接");
            return false;
        }

        try {
            int addr = Integer.parseInt(address.replace("HR_", ""));
            int val = Integer.parseInt(value.toString());

            master.writeSingleRegister(addr, val);

            logger.debug("成功写入寄存器 {}: {}", address, value);
            return true;

        } catch (Exception e) {
            logger.error("写入 Modbus RTU 数据时出错", e);
            return false;
        }
    }

    @Override
    public String getProtocolType() {
        return "Modbus RTU";
    }

    @Override
    public String getConnectionInfo() {
        return String.format("Modbus RTU - 端口: %s, 波特率: %d, 单元ID: %d",
            portName, baudRate, unitId);
    }
}
