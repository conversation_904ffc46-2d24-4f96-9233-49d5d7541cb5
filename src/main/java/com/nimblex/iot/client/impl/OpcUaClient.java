package com.nimblex.iot.client.impl;

import com.nimblex.iot.client.ProtocolClient;
import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.api.config.OpcUaClientConfig;
import org.eclipse.milo.opcua.stack.client.DiscoveryClient;
import org.eclipse.milo.opcua.stack.core.AttributeId;
import org.eclipse.milo.opcua.stack.core.Identifiers;
import org.eclipse.milo.opcua.stack.core.security.SecurityPolicy;
import org.eclipse.milo.opcua.stack.core.types.builtin.*;
import org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.UInteger;
import org.eclipse.milo.opcua.stack.core.types.enumerated.MessageSecurityMode;
import org.eclipse.milo.opcua.stack.core.types.enumerated.TimestampsToReturn;
import org.eclipse.milo.opcua.stack.core.types.structured.EndpointDescription;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadValueId;
import org.eclipse.milo.opcua.stack.core.types.structured.WriteValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * OPC UA 客户端实现
 */
public class OpcUaClient implements ProtocolClient {

    private static final Logger logger = LoggerFactory.getLogger(OpcUaClient.class);

    private final String endpointUrl;
    private final SecurityPolicy securityPolicy;
    private final MessageSecurityMode messageSecurityMode;
    private final String username;
    private final String password;
    private final List<String> nodeIds;

    private org.eclipse.milo.opcua.sdk.client.OpcUaClient opcUaClient;
    private boolean connected = false;

    public OpcUaClient(String endpointUrl, String securityPolicy, String messageSecurityMode,
                      String username, String password, List<String> nodeIds) {
        this.endpointUrl = endpointUrl;
        this.securityPolicy = SecurityPolicy.valueOf(securityPolicy);
        this.messageSecurityMode = MessageSecurityMode.valueOf(messageSecurityMode);
        this.username = username;
        this.password = password;
        this.nodeIds = nodeIds;
    }

    @Override
    public boolean connect() {
        try {
            logger.info("正在连接到 OPC UA 服务器: {}", endpointUrl);

            // 发现端点
            List<EndpointDescription> endpoints = DiscoveryClient.getEndpoints(endpointUrl).get();
            EndpointDescription endpoint = endpoints.stream()
                .filter(e -> e.getSecurityPolicyUri().equals(securityPolicy.getUri()))
                .filter(e -> e.getSecurityMode() == messageSecurityMode)
                .findFirst()
                .orElse(endpoints.get(0));

            // 创建客户端配置
            OpcUaClientConfig config = OpcUaClientConfig.builder()
                .setApplicationName(LocalizedText.english("IoT Gateway Client"))
                .setApplicationUri("urn:iot:gateway:client")
                .setEndpoint(endpoint)
                .build();

            // 创建客户端
            client = OpcUaClient.create(config);

            // 连接
            client.connect().get();
            connected = true;

            logger.info("成功连接到 OPC UA 服务器");
            return true;

        } catch (Exception e) {
            logger.error("连接 OPC UA 服务器失败", e);
            connected = false;
            return false;
        }
    }

    @Override
    public void disconnect() {
        if (client != null && connected) {
            try {
                client.disconnect().get();
                connected = false;
                logger.info("已断开 OPC UA 连接");
            } catch (Exception e) {
                logger.error("断开 OPC UA 连接时出错", e);
            }
        }
    }

    @Override
    public boolean isConnected() {
        return connected && client != null;
    }

    @Override
    public Map<String, Object> readData() {
        Map<String, Object> data = new HashMap<>();

        if (!isConnected()) {
            logger.warn("OPC UA 客户端未连接");
            return data;
        }

        try {
            // 构建读取请求
            List<ReadValueId> readValueIds = nodeIds.stream()
                .map(nodeId -> new ReadValueId(
                    NodeId.parse(nodeId),
                    AttributeId.Value.uid(),
                    null,
                    QualifiedName.NULL_VALUE))
                .collect(java.util.stream.Collectors.toList());

            // 执行读取
            CompletableFuture<DataValue[]> future = client.readValues(0.0, TimestampsToReturn.Both, readValueIds);
            DataValue[] dataValues = future.get();

            // 处理结果
            for (int i = 0; i < nodeIds.size() && i < dataValues.length; i++) {
                DataValue dataValue = dataValues[i];
                if (dataValue.getStatusCode().isGood()) {
                    data.put(nodeIds.get(i), dataValue.getValue().getValue());
                } else {
                    logger.warn("读取节点 {} 失败: {}", nodeIds.get(i), dataValue.getStatusCode());
                }
            }

            logger.debug("成功读取 {} 个节点的数据", data.size());

        } catch (Exception e) {
            logger.error("读取 OPC UA 数据时出错", e);
        }

        return data;
    }

    @Override
    public boolean writeData(String address, Object value) {
        if (!isConnected()) {
            logger.warn("OPC UA 客户端未连接");
            return false;
        }

        try {
            NodeId nodeId = NodeId.parse(address);
            Variant variant = new Variant(value);
            DataValue dataValue = new DataValue(variant, null, null);

            WriteValue writeValue = new WriteValue(
                nodeId,
                AttributeId.Value.uid(),
                null,
                dataValue
            );

            CompletableFuture<StatusCode[]> future = client.writeValues(List.of(writeValue));
            StatusCode[] statusCodes = future.get();

            if (statusCodes.length > 0 && statusCodes[0].isGood()) {
                logger.debug("成功写入节点 {}: {}", address, value);
                return true;
            } else {
                logger.warn("写入节点 {} 失败: {}", address, statusCodes[0]);
                return false;
            }

        } catch (Exception e) {
            logger.error("写入 OPC UA 数据时出错", e);
            return false;
        }
    }

    @Override
    public String getProtocolType() {
        return "OPC UA";
    }

    @Override
    public String getConnectionInfo() {
        return String.format("OPC UA - 端点: %s, 安全策略: %s, 消息安全模式: %s",
            endpointUrl, securityPolicy, messageSecurityMode);
    }
}
