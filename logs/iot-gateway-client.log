2025-06-18 16:53:28.245 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main - === IoT ����Э��ͻ��� Demo ===
2025-06-18 16:53:28.245 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main - ֧��Э��: OPC DA, OPC UA, Modbus TCP, Modbus RTU
2025-06-18 16:53:28.245 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main - �����ļ�: src/main/resources/application.properties
2025-06-18 16:53:28.309 [com.nimblex.iot.server.Main.main()] INFO  c.nimblex.iot.config.ProtocolConfig - �ɹ����������ļ�
2025-06-18 16:53:28.460 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.service.ProtocolClientService - ��������Э��ͻ��˷���...
2025-06-18 16:53:28.469 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.factory.ProtocolClientFactory - ���ڴ��� OPC_UA �ͻ���
2025-06-18 16:53:28.472 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.service.ProtocolClientService - �����ͻ��˳ɹ�: OPC UA - �˵�: opc.tcp://localhost:4840, ��ȫ����: None, ��Ϣ��ȫģʽ: None
2025-06-18 16:53:28.472 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.service.ProtocolClientService - ��������... (�� 1/3 ��)
2025-06-18 16:53:28.473 [com.nimblex.iot.server.Main.main()] INFO  c.n.iot.client.impl.OpcUaClient - �������ӵ� OPC UA ������: opc.tcp://localhost:4840
2025-06-18 16:53:29.475 [com.nimblex.iot.server.Main.main()] WARN  c.n.iot.client.impl.OpcUaClient - OPC UA �ͻ��˵�ǰΪģ��ʵ��
2025-06-18 16:53:29.477 [com.nimblex.iot.server.Main.main()] INFO  c.n.iot.client.impl.OpcUaClient - �ɹ����ӵ� OPC UA ��������ģ�⣩
2025-06-18 16:53:29.478 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.service.ProtocolClientService - ���ӳɹ�
2025-06-18 16:53:29.501 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.service.ProtocolClientService - ��ʱ��ȡ���������������: 5000ms
2025-06-18 16:53:29.502 [com.nimblex.iot.server.Main.main()] INFO  c.n.i.service.ProtocolClientService - Э��ͻ��˷��������ɹ�
2025-06-18 16:53:29.503 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main - ���������ɹ����ͻ�����Ϣ: OPC UA - �˵�: opc.tcp://localhost:4840, ��ȫ����: None, ��Ϣ��ȫģʽ: None
2025-06-18 16:53:29.504 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main - ����������в���:
2025-06-18 16:53:29.505 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main -   status  - �鿴״̬
2025-06-18 16:53:29.506 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main -   read    - �ֶ���ȡһ������
2025-06-18 16:53:29.506 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main -   write   - д������ (��ʽ: write <��ַ> <ֵ>)
2025-06-18 16:53:29.507 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main -   stop    - ֹͣ����
2025-06-18 16:53:29.507 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main -   start   - ��������
2025-06-18 16:53:29.507 [com.nimblex.iot.server.Main.main()] INFO  com.nimblex.iot.server.Main -   quit    - �˳�����
2025-06-18 16:53:34.503 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:53:34.561 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":170}
2025-06-18 16:53:34.562 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:53:39.502 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:53:39.518 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":661}
2025-06-18 16:53:39.518 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:53:44.511 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:53:44.513 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":543}
2025-06-18 16:53:44.513 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:53:49.501 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:53:49.502 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":619}
2025-06-18 16:53:49.502 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:53:54.511 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:53:54.512 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":698}
2025-06-18 16:53:54.513 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:53:59.501 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:53:59.502 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":710}
2025-06-18 16:53:59.504 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:04.501 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:04.502 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":542}
2025-06-18 16:54:04.504 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:09.512 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:09.513 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":588}
2025-06-18 16:54:09.514 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:14.514 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:14.515 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":16}
2025-06-18 16:54:14.516 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:19.510 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:19.510 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":643}
2025-06-18 16:54:19.510 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:24.503 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:24.503 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":512}
2025-06-18 16:54:24.504 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:29.575 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:29.620 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":209}
2025-06-18 16:54:29.692 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:34.500 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:34.500 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":104}
2025-06-18 16:54:34.500 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:39.503 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:39.504 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":806}
2025-06-18 16:54:39.504 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:44.509 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:44.509 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":498}
2025-06-18 16:54:44.510 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:49.508 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:49.509 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":937}
2025-06-18 16:54:49.509 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:54.512 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:54.513 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":727}
2025-06-18 16:54:54.513 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:54:59.504 [ProtocolClient-Reader] DEBUG c.n.iot.client.impl.OpcUaClient - �ɹ���ȡ 1 ���ڵ�����ݣ�ģ�⣩
2025-06-18 16:54:59.504 [ProtocolClient-Reader] INFO  c.n.i.service.ProtocolClientService - ��ȡ����: {"ns=2;i=2":957}
2025-06-18 16:54:59.505 [ProtocolClient-Reader] DEBUG c.n.i.service.ProtocolClientService - ��������: 1 �����ݵ�
2025-06-18 16:55:02.967 [Thread-1] INFO  com.nimblex.iot.server.Main - ���ڹر�Ӧ�ó���...
2025-06-18 16:55:02.967 [Thread-1] INFO  c.n.i.service.ProtocolClientService - ����ֹͣЭ��ͻ��˷���...
2025-06-18 16:55:02.969 [Thread-1] INFO  c.n.iot.client.impl.OpcUaClient - �ѶϿ� OPC UA ����
2025-06-18 16:55:02.970 [Thread-1] INFO  c.n.i.service.ProtocolClientService - Э��ͻ��˷�����ֹͣ
2025-06-18 16:56:27.810 [main] INFO  com.nimblex.iot.server.Main - === IoT 网关协议客户端 Demo ===
2025-06-18 16:56:27.814 [main] INFO  com.nimblex.iot.server.Main - 支持协议: OPC DA, OPC UA, Modbus TCP, Modbus RTU
2025-06-18 16:56:27.814 [main] INFO  com.nimblex.iot.server.Main - 配置文件: src/main/resources/application.properties
2025-06-18 16:56:27.907 [main] INFO  c.nimblex.iot.config.ProtocolConfig - 成功加载配置文件
